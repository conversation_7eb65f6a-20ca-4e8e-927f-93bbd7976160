import { NextRequest, NextResponse } from 'next/server';

// Protected routes that require authentication
const protectedRoutes = ['/dashboard'];
// Public routes that should redirect to dashboard if already authenticated
const publicAuthRoutes = ['/login'];

export async function middleware(req: NextRequest) {
  const path = req.nextUrl.pathname;

  // Temporarily simplified - just check for NextAuth cookies
  const nextAuthCookie = req.cookies.get('next-auth.session-token') ||
                        req.cookies.get('__Secure-next-auth.session-token');
  const isAuthenticated = !!nextAuthCookie;

  // Redirect to login if accessing protected route without authentication
  if (protectedRoutes.some(route => path.startsWith(route)) && !isAuthenticated) {
    return NextResponse.redirect(new URL('/login', req.url));
  }

  // Redirect to dashboard if accessing login page while authenticated
  if (publicAuthRoutes.includes(path) && isAuthenticated) {
    return NextResponse.redirect(new URL('/dashboard', req.url));
  }

  return NextResponse.next();
}

// Configure which routes middleware should run on
export const config = {
  matcher: [
    // Include the specific paths you need middleware for
    '/dashboard',
    '/dashboard/:path*',
    '/login',
    '/profile',
    '/profile/:path*'
    // Add other paths as needed
  ],
};


